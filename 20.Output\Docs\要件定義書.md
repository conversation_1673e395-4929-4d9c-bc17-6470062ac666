# バーコード（QRコード）を使った製品管理システム要件定義書

## 1. 概要

本システムは、バーコード（QRコード）技術を活用した製品管理システムである。製品の受取から出荷まで、一連の業務プロセスをデジタル化し、効率的な在庫管理と品質管理を実現する。

### 1.1 システム目的
- QRコードによる製品識別と追跡
- 製品の受取・検査・保管・出荷の一元管理
- 作業効率の向上と人的ミスの削減
- リアルタイムな在庫状況の把握

### 1.2 対象製品
- 機器本体（PT1）
- 薬液タンク（PT2）
- 電池（PT3）
- ACアダプター（PT4）
- ポンプ（PT5）
- ノズル（PT6）
- 取扱説明書（PT7）
- 薬液A（PT8）
- 薬液B（PT9）

## 2. 機能要件

### 2.1 製品識別・管理機能

#### 2.1.1 QRコード管理
- **要件**: 各製品にシリアルNoを含むQRコードを付与
- **詳細**: 
  - QRコードの内容はシリアルNoである
  - バッテリとACアダプターにはシリアルNoとQRコードが表示されている
  - QRコードの読み取り機能
  - QRコードの生成・印刷機能

#### 2.1.2 製品分類管理
- **要件**: 製品を管理区分に応じて分類
- **分類**:
  - シリアルNo管理: 個体管理が必要な製品
  - ロットNo管理: ロット単位での管理が必要な製品
  - 個体管理/消耗品: 個別管理が必要な消耗品
  - 消耗品: 一般的な消耗品

#### 2.1.3 製品構成管理
- **要件**: 製品の構成要素と管理方法を定義
- **管理項目**:
  - 製品No
  - 構成部品
  - 製造元
  - 管理No
  - 管理区分

### 2.2 受取・検査機能

#### 2.2.1 製品受取機能
- **要件**: 製品受取時の検収対応
- **機能**:
  - 現品確認（数量、受取検査実施、製造時検査記録チェック）
  - 納品書と現品の照合
  - 型番と数量のチェック
  - 受取検査（外観検査、ラベル記載内容・PSEマーク表示確認）
  - 製造時検査記録のチェック

#### 2.2.2 検査記録管理
- **要件**: 検査結果の記録と管理
- **記録項目**:
  - 確認日/検査日
  - 確認者/チェック者
  - 確認/検査を行った対象（管理No）
  - 検査結果（合格/不合格）

#### 2.2.3 システム連携機能
- **要件**: 作業手順のシステム化
- **機能**:
  - シリアルNo（QRコード）をQR Readerで読み込み
  - シリアルNoをDB登録（新規登録の場合）
  - 登録した管理NoをPCに表示
  - 登録数を表示

### 2.3 出荷時構成管理機能

#### 2.3.1 出荷構成定義
- **要件**: 出荷時の製品構成を管理
- **構成要素**:
  - BoxNo（出荷単位）
  - 基本セット1・2の同梱数
  - 個別数量指定
  - 出荷先情報

#### 2.3.2 出荷内容構成管理
- **要件**: 出荷品目の詳細管理
- **管理項目**:
  - 出荷品構成（機器本体、薬液タンク、電池、ACアダプター、ポンプ、ノズル、取扱説明書）
  - 各品目の数量
  - BoxNo割り当て
  - 出荷先情報

### 2.4 薬液管理機能

#### 2.4.1 薬液分け/充填管理
- **要件**: 薬液の小分け・充填作業管理
- **機能**:
  - 薬液Aの薬液タンクへの充填
  - 作業記録作成（タンクNo、作業日、作業者、点検者）、保管
  - 薬液Bの薬液タンクへの充填
  - 作業記録作成（タンクNo、作業日、作業者、点検者）、保管

#### 2.4.2 在庫管理
- **要件**: 在庫状況の確認と管理
- **機能**:
  - 在庫状況確認
  - 追加手配の検討
  - 消耗品（薬液、薬液タンク、ノズル）の在庫管理

### 2.5 全体プロセス管理機能

#### 2.5.1 業務フロー管理
- **要件**: 製品管理の全体プロセスを管理
- **プロセス**:
  1. 製品の受取（製品/電池/アダプタ/薬剤タンク）
  2. 部品の受取（注入ノズル）
  3. 受取検査
  4. 組立
  5. 試運転（動作確認）
  6. 保管
  7. 格納
  8. 受注
  9. 梱包
  10. 出荷
  11. 発送
  12. 運転時メンテナンス
  13. 廃棄

#### 2.5.2 受注管理
- **要件**: 受注に応じた製品準備
- **機能**:
  - 受注時保管場所の確認
  - 引き当て
  - 製品の保管・格納時保管場所の登録
  - 発送時出荷先の登録
  - 受注（一式、電池のみ、薬剤のみ、その他）

## 3. 非機能要件

### 3.1 システム構成要件
- **表示装置**: Display または Tablet
- **入力装置**: Keyboard または Tablet
- **QRコードReader**: QRコード読み取り用
- **QRコードWriter**: QRコード生成・印刷用
- **データベース**: 現存DB および 新規DB

### 3.2 性能要件
- **応答時間**: QRコード読み取り後の応答時間は3秒以内
- **同時利用者数**: 最大10名の同時利用に対応
- **データ保存**: 検査記録は最低5年間保存

### 3.3 セキュリティ要件
- **アクセス制御**: ユーザー認証機能
- **データ保護**: 製品情報の暗号化保存
- **監査ログ**: 全ての操作履歴を記録

## 4. 業務フロー

### 4.1 製品受取フロー
1. 製品到着
2. QRコード読み取り
3. システムへの登録
4. 現品確認
5. 受取検査実施
6. 検査記録作成
7. 保管場所への格納

### 4.2 出荷準備フロー
1. 受注内容確認
2. 必要製品の引き当て
3. 出荷構成の確認
4. 梱包作業
5. 出荷記録作成
6. 発送手配

### 4.3 薬液管理フロー
1. 薬液受取
2. 品質確認
3. 小分け・充填作業
4. 作業記録作成
5. 在庫更新
6. 保管

## 5. データ要件

### 5.1 製品マスタ
- 製品No
- 製品名
- 製造元
- 管理区分
- シリアルNo/ロットNo

### 5.2 在庫管理データ
- 管理No
- 保管場所
- 在庫数量
- 入庫日
- 出庫予定日

### 5.3 検査記録データ
- 検査日
- 検査者
- 検査対象
- 検査結果
- 備考

### 5.4 出荷管理データ
- 出荷No
- 出荷日
- 出荷先
- 出荷構成
- BoxNo

## 6. 制約事項

### 6.1 技術制約
- 既存システムとの連携が必要
- QRコードの読み取り精度の確保
- データベースの移行作業が必要

### 6.2 業務制約
- 現行業務との並行運用期間が必要
- 作業者への教育・訓練が必要
- 段階的な導入が必要

### 6.3 運用制約
- PSE法対応が必要
- 製造時検査記録の保管義務
- トレーサビリティの確保

---

**以上**
